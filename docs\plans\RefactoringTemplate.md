Le suivi de ce template est **OBLIGATOIRE** ! Il est **INTERDIT** de passer outre la moindre étape de ce template lors de la création d'un plan !

**L’usage de Git est réservé à l’utilisateur final (auteur réel du refactoring). Le présent document ne prévoit aucune exécution de commandes Git automatique ou manuelle.**

> **Directive de Commit (pour l'IA) :** À la fin de chaque étape majeure (marquée par le symbole ✅🔒), tu **DOIS** avertir explicitement l'utilisateur d'effectuer un commit en utilisant la phrase exacte fournie.
>
> **Recommandation de Stratégie de Commit (pour l'utilisateur) :** Pour un historique propre et compréhensible, il est recommandé de créer des commits atomiques qui correspondent aux étapes de ce plan. Par exemple :
> - `feat: Add Characterization Tests for [Feature]` (après l'Étape 1)
> - `refactor: Introduce I[NewService] and implementations` (après l'Étape 2)
> - `refactor(ViewModel): Migrate [Unit] logic to new Manager` (à la fin de chaque itération de l'Étape 3)

Le setup est **Windows 10** et **PowerShell** : utiliser les commandes en conséquence.

# **Modèle de Plan de Refactoring (Version 25.0)**

- **Titre du Refactoring :** `[Nom de la Méthode/Classe à refactorer]`
- **Date :** `AAAA-MM-JJ`
- **Auteur(s) :** `[Votre Nom/Équipe]`
- **Version :** `9.0`

> ### **Directives Fondamentales (Règles Non Négociables)**
>
> #### **1. La Source de Vérité Technique : Le Code Source Réel**
> - **L'analyse doit se baser EXCLUSIVEMENT sur le code source fourni.** Toute connaissance générique ou information issue d'anciens documents doit être ignorée. Les suppositions sont interdites.
>
> #### **2. La Source de Vérité Fonctionnelle : Le Document de Spécifications**
> - Le document `docs/fonctions.md` est la **Source de Vérité Absolue** pour le comportement attendu de la fonctionnalité.
> - Le but du refactoring est de préserver ce comportement fonctionnel, et non les bugs ou les effets de bord de l'ancienne implémentation.
>
> #### **3. La Source de Vérité Architecturale : L'Architecture Cible**
> - L'objectif est de transformer le code pour atteindre la **nouvelle architecture définie**. Le code de production refactorisé est la référence technique.
> - Les tests sont des outils au service de cette transformation. Ils doivent être **modifiés, alignés ou supprimés** pour valider la nouvelle architecture.
>
> #### **4. Règle d'Or : Interdiction de Modifier le Nouveau Code pour d'Anciens Tests**
> - Un test qui échoue après une modification du code de production n'est **PAS une régression**. C'est un **SIGNAL ATTENDU** que le test est devenu obsolète.
> - Il est **FORMELLEMENT INTERDIT** de modifier le code de production refactorisé pour faire passer un ancien test. L'unique action autorisée est de **modifier le test** pour qu'il corresponde à la nouvelle réalité du code.
>
> #### **5. Règle d'Or des Tests : Le Principe de la Boîte Noire**
> - Les tests **DOIVENT** interagir avec l'unité sous test (UUT) via son **API publique EXCLUSIVEMENT**. L'intérieur de l'UUT est considéré comme une "boîte noire".
> - **Exemple Concret :** Pour un ViewModel, cela signifie que les tests ne doivent interagir qu'avec ses **propriétés publiques** et ses **`ICommand`s**. Il est **FORMELLEMENT INTERDIT** d'appeler des méthodes de bas niveau sur ses dépendances internes (ex: appeler `_historyModule.RemoveItemAsync()` directement depuis un test). Le test **DOIT** exécuter `viewModel.SupprimerElementCommand.Execute()` pour valider le workflow utilisateur complet.

---

## 1. 📊 **Analyse et Diagnostic Initial**

### 1.1. Contexte et Localisation
- **Composant :** `[Nom de la Classe ou du Module. Ex: LoggingService]`
- **Fichier(s) :** `[Chemin(s) vers le/les fichier(s). Ex: src/Services/LoggingService.cs]`
- **Lignes de code concernées :** `[Plage de lignes. Ex: 372-523]`
- **Description de la fonctionnalité :** `[Décrire brièvement ce que fait le code actuel. Ex: "Méthode privée monolithique qui centralise l'écriture des logs vers la console, le fichier de débogage et un buffer interne."]`

### 1.2. Métriques Actuelles (avant refactoring)
*(Remplir cette section après une analyse avec un outil comme NDepend, SonarQube, ou l'analyseur de Visual Studio)*

| Métrique | Valeur | Statut | Commentaire |
| :--- | :--- | :--- | :--- |
| **Crap Score** | `[ex: ~180]` | ❌ **CRITIQUE** | `[ex: Extrêmement difficile à maintenir]` |
| **Complexité Cyclomatique**| `[ex: 15]` | ❌ **ÉLEVÉE** | `[ex: Trop de branches conditionnelles]` |
| **Lignes de Code** | `[ex: 151]` | ⚠️ **ÉLEVÉ** | `[ex: Méthode monolithique]` |
| **Couverture de Test** | `[ex: 5%]` | ❌ **FAIBLE** | `[ex: Non testé directement, seulement via les effets de bord]` |
| **Responsabilités (SRP)** | `[ex: 11]` | ❌ **VIOLATION** | `[ex: Fait du formatage, du buffering, de l'écriture, etc.]` |

### 1.3. Problématiques Identifiées
*(Liste à puces des problèmes concrets)*
- **Complexité Excessive :** `[ex: Logique de 'if' imbriqués sur 5 niveaux rendant la lecture et la modification impossibles sans risque.]`
- **Performance :** `[ex: Utilisation systématique de StackTrace.Parse() à chaque appel, créant un bottleneck de performance connu.]`
- **Testabilité Nulle :** `[ex: Couplage fort avec des API statiques (System.IO.File, Console) et des ressources globales, empêchant tout test unitaire isolé.]`
- **Maintenabilité Faible :** `[ex: Pour ajouter une nouvelle cible de log (ex: base de données), il faudrait modifier cette méthode de 150 lignes, en risquant de casser les 11 autres responsabilités.]`
- **Violation de Principes :** `[ex: Violation flagrante du Single Responsibility Principle (SRP) et du Open/Closed Principle (OCP).]`
- **Risque de Câblage Défaillant :** `[ex: L'introduction de nouvelles classes via DI augmente le risque d'oublis d'enregistrement ou de constructeurs incorrects, menant à des régressions silencieuses.]`

---

## 2. 🎯 **Objectifs et Critères de Succès**

### 2.1. Objectifs Principaux
*(Liste des buts clairs et mesurables)*
- [ ] **Réduire la Complexité Cyclomatique** de `[valeur actuelle]` à **`< 5`**.
- [ ] **Assurer une transition 100% sécurisée** en validant l'absence de régression fonctionnelle, **telle que définie dans `docs/fonctions.md`**, via une validation manuelle par l’utilisateur.
- [ ] **Atteindre une couverture de test** de **`> 85%`** sur la **nouvelle architecture**.
- [ ] **Migrer [nombre] unités de refactoring** de manière incrémentale en utilisant le Cycle de Migration.
- [ ] **Éliminer les couplages forts** en introduisant des abstractions (interfaces).
- [ ] **Améliorer la performance** en remplaçant les opérations coûteuses.
- [ ] **Assurer un câblage DI robuste** en validant que tous les nouveaux composants peuvent être construits sans erreur.

### 2.2. Périmètre (Ce qui sera fait / ne sera pas fait)
- **Inclus dans le périmètre :**
  - Refactoring complet de la méthode `[Nom de la Méthode]`.
  - Introduction des nouvelles interfaces et classes de service nécessaires.
  - Migration incrémentale des unités de refactoring via le Cycle de Migration.
  - Création d'un harnais de tests de caractérisation et de nouveaux tests unitaires pour la nouvelle architecture.
  - Validation manuelle obligatoire par l’utilisateur avant la suppression finale du code legacy.
- **Exclus du périmètre (Non-Objectifs) :**
  - Remplacement de la solution par une librairie tierce (ex: Serilog, NLog).
  - Modification du format de sortie des logs.
  - Ajout de nouvelles fonctionnalités de logging non existantes.

### 2.3. Critères de Succès ("Definition of Done")
1. ✅ Le comportement externe de l'application, validé par le harnais de sécurité et une validation manuelle par l’utilisateur, est **identique au comportement décrit dans `docs/fonctions.md`**.
2. ✅ Les métriques de qualité de la nouvelle architecture (complexité < 5, couverture > 85%) sont atteintes.
3. ✅ Aucune régression fonctionnelle ou de performance n'est détectée lors de la validation manuelle.
4. ✅ L'ancienne implémentation est **entièrement supprimée** du code source, ne laissant que l'architecture cible, après réception du "FEU VERT" de l’utilisateur.

---

## 3. 🛡️ **Plan de Sécurité et Gestion des Risques**

### 3.1. Risques Identifiés
| Risque | Probabilité | Impact | Mesure de Mitigation |
| :--- | :--- | :--- | :--- |
| **Harnais de test "aveugle"** | Élevée | Critique | **Étape 1.2 :** Validation obligatoire du harnais par "test de mutation" pour prouver sa sensibilité. |
| **Perte de performance** | Faible | Élevé | **Benchmarks** avant/après sur les chemins de code critiques (Étape 1 et **Étape 5**). |

### 3.2. Stratégie du Harnais de Sécurité
- Des **tests de caractérisation**, basés sur les spécifications du document **`docs/fonctions.md`**, seront écrits pour verrouiller le comportement externe observable actuel.
- **Exemple de test à créer :** Un test vérifiera que `SupprimerToutCommand` préserve bien les éléments épinglés, comme décrit dans la section "Suppression en Lot" de `fonctions.md`.
- Leur but est de valider le **comportement fonctionnel externe**, et non l'implémentation interne qui est destinée à être démolie. La pertinence de ces tests sera obligatoirement validée par test de mutation.
- Une validation manuelle par l’utilisateur sera effectuée à chaque itération du Cycle de Migration (Étape 3) pour confirmer l’absence de régressions avant de passer à l’unité suivante.

---

## 4. 🎯 Stratégie de Validation Détaillée
*Cette section définit les différents types de validations qui sécuriseront la nouvelle architecture.*

### 4.1. Les Piliers de la Validation
| Pilier | Type de Validation | Objectif et Périmètre | Étape Clé |
| :--- | :--- | :--- | :--- |
| **Pilier 1**<br/>*(Fondation)* | **Validation de l'Assemblage** | **Vérifier que le "plan de montage" (HostConfiguration.cs) est correct.** S'assurer que le conteneur DI peut construire l'objet principal avec toutes ses nouvelles dépendances. | **Étape 3 (Sous-étape A)** |
| **Pilier 2**<br/>*(Unités)* | **Validation Unitaire** | **Vérifier chaque nouveau composant en isolation totale.** | **Étape 2** |

### 4.2. Liste des Tests Spécifiques à Créer
- [ ] **Tests Unitaires :** Pour chaque nouvelle classe créée (`[ex: ConsoleLogTarget, LogEntryFactory, ...]`).
- [ ] **Tests d'Intégration :** Pour la nouvelle méthode d'orchestration (`[ex: WriteToLog_V2]`) et ses collaborateurs.
- [ ] **Tests de Concurrence (Thread-Safety) :** Si la fonctionnalité est multithread.
- [ ] **Tests de Performance :** Le benchmark créé en Étape 1 sera ré-exécuté en Étape 4.
- [ ] **Tests sur Thread d'Interface Utilisateur (UI-Thread / STA) :** Si la fonctionnalité interagit avec l'UI.
- [ ] **Tests de Cas d'Erreur :** Vérifier le comportement en cas de dépendance défaillante (ex: disque plein).

---

## 5. 🏗️ **Plan d'Implémentation par Étapes**

### **Étape 0 : Pré-Vérifications et Audits**
- [ ] **Étape 0.0 : Validation de la Source de Vérité Fonctionnelle (NON NÉGOCIABLE).**
    - [ ] **Objectif :** S'assurer que le document `docs/fonctions.md` est complet, à jour et irréprochable avant de l'utiliser comme base pour le harnais de validation.
    - [ ] **Action :** Lire intégralement `docs/fonctions.md` et le comparer au comportement actuel de l'application.
    - [ ] **Checklist de Validation Fonctionnelle :**
        - [ ] Le document décrit-il toutes les fonctionnalités critiques du composant à refactorer ? (Oui/Non)
        - [ ] Les règles métier décrites sont-elles claires et non ambiguës ? (Oui/Non)
        - [ ] Le comportement décrit correspond-il à l'intention attendue, même si l'implémentation actuelle est boguée ? (Oui/Non)
    - [ ] **Demande de Confirmation Obligatoire :**
        - [ ] Poser explicitement la question suivante à l'utilisateur et attendre sa confirmation avant de poursuivre :
        - [ ] **"🚨 VALIDATION REQUISE. J'ai analysé `docs/fonctions.md`. Confirmez-vous que ce document est la source de vérité fonctionnelle irréprochable pour la suite de ce refactoring ? Répondez par 'FONCTIONS VALIDÉES' pour continuer."**
- [ ] **Étape 0.1 : Vérification du Contexte Technique (OBLIGATOIRE)**
    - [ ] **Objectif :** Ancrer l'analyse dans la réalité du code source pour éviter toute supposition.
    - [ ] **Action :** Analyser les fichiers de code pertinents et lister les informations suivantes.
        - **Modèles de Données Clés :**
            - **Classe `[NomDeLaClasse]` :** `[Lister les propriétés EXACTES]`
            - **Enum `[NomDeLEnum]` :** `[Lister les valeurs EXACTES]`
        - **Contrats et Injections Clés :**
            - **DTO/Interface `[NomDuContrat]` :** `[Lister les dépendances EXACTES]`
        - **Analyse Complémentaire par l'IA :**
            - **Autres détails techniques critiques identifiés :** `[Lister ici tout autre point (classe, méthode, propriété) que vous jugez essentiel pour la réussite de ce refactoring spécifique et qui n'est pas déjà couvert ci-dessus. Justifiez brièvement pourquoi chaque point est critique.]`
- [ ] **Étape 0.2 : Prendre connaissance de la totalité du projet** (architecture, dépendances, tests existants, etc.)
- [ ] **Étape 0.3 : Identifier les tests existants couplés à l'ancienne implémentation**
- [ ] **Étape 0.4 : Vérifier la couverture des tests existants**
- [ ] **Étape 0.5 : Identifier les parties critiques de la fonctionnalité (ex: accès fichier, appel BDD, service externe)**
- [ ] **Étape 0.6 : Audit de Performance Préventif.**
    - [ ] **Identifier les Chemins de Code Critiques :** Lister les parties du code existant qui sont sensibles à la performance (ex: boucles serrées, I/O fréquents, grosses allocations mémoire).
    - [ ] `[Lister ici les zones de performance critiques identifiées]`

### **Étape 1 : Création et Validation du Harnais de Sécurité (Obligatoire)** (Durée estimée : `[ex: 1.5 jours]`)
*Aucune autre Étape ne peut commencer tant que celle-ci n'est pas entièrement validée.*

- [ ] **Étape 1.1 : Écriture du Harnais de Caractérisation.**
    - [ ] Écrire une série de tests qui capturent le comportement externe observable actuel.
    - [ ] ✅ **Exécuter ces tests.** Ils doivent tous passer.
- [ ] **Étape 1.2 : Validation du Harnais par Test de Mutation (Le Test de Confiance).**
    - [ ] **a) Identifier les dépendances critiques** de la méthode (ex: accès fichier, appel BDD).
    - [ ] **b) Pour la première dépendance critique :**
        - [ ] **1.** Introduire une panne contrôlée dans le code de production (ex: commenter `File.WriteAllText(...)`).
        - [ ] **2.** ✅ **Exécuter le harnais de tests.**
        - [ ] **3. Analyser le résultat :**
            - ❌ **Si le harnais ÉCHOUE** : Parfait ! Il est sensible à cette dépendance. Passez à l'étape 4.
            - ✅ **Si le harnais PASSE** : **ALERTE.** Le harnais est aveugle à un comportement que nous devons préserver. **Retourner à l'étape 1.1** pour enrichir le harnais.
        - [ ] **4.** Annuler la panne et ✅ **vérifier que le harnais repasse au vert.**
    - [ ] **c) Répéter l'étape b) pour TOUTES les dépendances critiques identifiées.**
- [ ] **Étape 1.3 : Documentation et validation**
    - [ ] Mettre à jour **toutes les sections nécessaires** de ce document.
    - [ ] ✅ **Exécuter TOUTE la suite de tests du harnais de sécurité.** Elle doit passer à 100%.
- [ ] ✅🔒 **FIN DE L'ÉTAPE 1.**
- [ ] **Notification de Commit :**
    - [ ] Avertir l'utilisateur avec la phrase exacte : **"🚨 Il est maintenant recommandé d'effectuer un commit pour valider et sécuriser l'Étape 1 (Création du Harnais) avant de poursuivre. 🚨"**

### **Étape 2 : Construction Parallèle - L'Échafaudage** (Durée estimée : `[ex: 2 jours]`)
- [ ] **Étape 2.1 :** Définir les nouvelles interfaces (`[ex: ILogTarget, ILogEntryFactory]`).
- [ ] **Étape 2.2 :** Créer les nouvelles classes d'implémentation.
    - [ ] **a) Remplir la Fiche d'Identité pour CHAQUE nouvelle classe :**
| Propriété | Description |
| :--- | :--- |
| **Nom de la Classe :** | `[Nom clair et précis, ex: FileLogTarget]` |
| **Namespace :** | `[Namespace complet, ex: Company.Project.Logging.Targets]` |
| **Interface Implémentée :** | `[Interface correspondante, ex: ILogTarget]` |
| **Responsabilité Unique (SRP) :** | `[Une seule phrase décrivant son unique rôle, ex: "Est responsable de l'écriture formatée d'une entrée de log dans un fichier texte."]` |
| **Dépendances (Constructeur) :** | `[Lister les interfaces requises, ex: IFileSystem, ILogFormatter]` |
    - [ ] **b) Écrire le code de la classe en respectant scrupuleusement sa Fiche d'Identité.**
    - [ ] **c) Validation Fonctionnelle OBLIGATOIRE :** Pour chaque méthode implémentée, **consulter `docs/fonctions.md`** et s'assurer que la logique respecte les spécifications.
    - [ ] **d) Documentation OBLIGATOIRE :** Pour chaque nouvelle classe et chaque nouvelle méthode publique, un commentaire XML `/// <summary>...</summary>` expliquant son rôle **DOIT** être ajouté.
- [ ] **Étape 2.3 :** Implémenter **tous les tests unitaires** pour chaque nouveau composant.
    - [ ] **a) Validation de Performance OBLIGATOIRE :** Si un nouveau composant remplace un "Chemin de Code Critique" identifié en **Étape 0.6**, valider que l'implémentation n'utilise pas d'anti-patterns de performance connus (ex: requêtes BDD dans une boucle, LINQ excessif sur de grosses collections, etc.).
    - [ ] **b) Validation Fonctionnelle OBLIGATOIRE :** Les tests unitaires **DOIVENT** inclure des scénarios qui valident les règles métier spécifiques de `docs/fonctions.md`.
- [ ] **Étape 2.4 :** Créer la nouvelle méthode d'orchestration `[NomRefactoredMethod]` qui coexistera avec l'ancienne.
> ### ✅ Checklist d'Auto-Validation de l'Étape
> - [ ] **Conformité Architecturale :** Le code produit respecte-t-il l'Architecture Cible et les Garde-fous ? (Oui/Non)
> - [ ] **Conformité Fonctionnelle :** La logique implémentée est-elle alignée avec `docs/fonctions.md` ? (Oui/Non)
> - [ ] **Documentation :** La documentation "au fil de l'eau" a-t-elle été complétée ? (Oui/Non)
> - [ ] **Tests :** Tous les programmes de validation unitaire requis pour cette étape ont-ils été créés et passent-ils ? (Oui/Non)
- [ ] ✅🔒 **FIN DE L'ÉTAPE 2.**
- [ ] **Notification de Commit :**
    - [ ] Avertir l'utilisateur avec la phrase exacte : **"🚨 Il est maintenant recommandé d'effectuer un commit pour valider et sécuriser l'Étape 2 (Construction des Nouveaux Composants) avant de poursuivre. 🚨"**

### **Étape 3 : Cycle de Migration Incrémentale** (Durée estimée : `[Variable]`)

> **Principe :** Le refactoring sera effectué de manière itérative, une "Unité de Migration" à la fois. Une Unité de Migration est un ensemble de code cohérent (ex: un manager et ses dépendances directes). Le cycle suivant est répété pour **chaque** unité.

- [ ] **3.1. Planification des Itérations.**
    - [ ] **Action :** Lister toutes les Unités de Migration à traiter, dans un ordre logique (des dépendances les plus faibles aux plus fortes).
    - [ ] **Liste des Unités de Migration :**
        - [ ] 1. `[Nom de la première Unité, ex: HistoryViewModelManager]`
        - [ ] 2. `[Nom de la deuxième Unité, ex: CommandViewModelManager]`
        - [ ] ...

---

#### **CYCLE DE MIGRATION (À RÉPÉTER POUR CHAQUE UNITÉ)**

> **Unité en cours de migration :** `[Reporter ici le nom de l'unité en cours]`

- [ ] **SOUS-ÉTAPE A : Validation de l'Assemblage de l'Unité (Le "Test du Premier Démarrage")**
    - [ ] **Objectif :** Vérifier que l'unité en cours et ses nouvelles dépendances sont correctement enregistrées dans le conteneur DI (`HostConfiguration.cs`).
    - [ ] **Action :** Exécuter le "Script de Vérification d'Assemblage" (créé lors de la première itération) qui tente de résoudre l'objet principal (`ClipboardHistoryViewModel`).
    - [ ] **Validation :**
        - [ ] ✅ **Si le script passe :** Le câblage est correct.
        - [ ] ❌ **Si le script échoue :** Corriger **UNIQUEMENT** `HostConfiguration.cs`, puis relancer jusqu'au succès.

- [ ] **SOUS-ÉTAPE B : Alignement des Appelants pour l'Unité**
    - [ ] **Objectif :** Migrer le code source pour utiliser la nouvelle logique de l'unité en cours.
    - [ ] **Action :** Identifier tous les points d'appel liés à la fonctionnalité de cette unité et les faire pointer vers le nouveau composant.
    - [ ] **Validation :** Exécuter le harnais de sécurité (`dotnet test`). Il doit continuer de passer à 100%.

- [ ] **SOUS-ÉTAPE C : Confinement et Alignement des Validations**
    - [ ] **Objectif :** Verrouiller l'ancienne logique de l'unité et mettre à jour les validations (tests) qui en dépendaient.
    - [ ] **Actions :**
        - [ ] 1. **Verrouiller** l'ancien code lié à cette unité (ex: le marquer `[Obsolete]`).
        - [ ] 2. **Lancer `dotnet test`** pour identifier les validations obsolètes (qui vont maintenant échouer).
        - [ ] 3. **Aligner ou Supprimer** chaque validation obsolète en suivant l'arbre de décision (Pertinent ? OUI -> Aligner, NON -> Supprimer).
    - [ ] **Validation :** La suite de validation complète passe de nouveau à 100%.

- [ ] **SOUS-ÉTAPE D : Validation Manuelle de l'Itération**
    - [ ] **Objectif :** Obtenir une confirmation humaine que la migration de l'unité n'a introduit aucune régression.
    - [ ] **Action obligatoire :** Demander explicitement à l'utilisateur :
    - [ ] **"🚨 VALIDATION MANUELLE DE L'UNITÉ `[Nom de l'Unité]` REQUISE. Veuillez vérifier que les fonctionnalités liées à cette unité fonctionnent correctement et confirmez si je peux passer à l'unité suivante en répondant par 'FEU VERT POUR L'UNITÉ SUIVANTE'."**

> **Fin du Cycle pour l'Unité :** `[Nom de l'unité en cours]`. Recommencer le cycle avec l'unité suivante de la liste.
- [ ] ✅🔒 **FIN DE L'ITÉRATION.**
- [ ] **Notification de Commit :**
    - [ ] Avertir l'utilisateur avec la phrase exacte : **"🚨 Il est maintenant recommandé d'effectuer un commit pour valider et sécuriser la migration de l'unité `[Nom de l'Unité]` avant de poursuivre. 🚨"**

---

### **Étape 4 : Nettoyage et Finalisation** (Durée estimée : `[ex: 0.5 jour]`)
- [ ] **Étape 4.1 :** Supprimer physiquement l'ancienne méthode `[NomOriginalMethod]`.
- [ ] **Étape 4.2 :** Renommer `[NomRefactoredMethod]` en `[NomOriginalMethod]`.
- [ ] **Étape 4.3 :** Supprimer tous les artéfacts privés liés à l'ancienne méthode.
- [ ] **Étape 4.4 :** ✅ **Exécuter une dernière fois l'intégralité des tests.**

### **Étape 5 : Validation Finale** (Durée estimée : `[ex: 0.5 jour]`)
- [ ] **Étape 5.1 :** Lancer le benchmark sur la nouvelle implémentation et comparer les résultats avec l'Étape 1.
- [ ] **Étape 5.2 :** Mesurer les métriques finales (Complexité, Couverture, etc.) et les inscrire dans la section 6.
- [ ] **Étape 5.3 :** Mettre à jour la documentation du code (commentaires XML, README).

### **Étape 6 : Documentation et Archivage** (Durée estimée : `[ex: 0.5 jour]`)
- [ ] **Étape 6.1 :** Mettre à jour **toutes les sections** de ce document pour refléter le travail effectué et les métriques finales.

---

## 6. 📊 **Validation Post-Refactoring**

### 6.1. Métriques Finales (après refactoring)
| Métrique | Valeur Cible | Valeur Atteinte | Statut |
| :--- | :--- | :--- | :--- |
| **Crap Score** | `< 10` | `[À remplir]` | `[À remplir]` |
| **Complexité Cyclomatique**| `< 5` | `[À remplir]` | `[À remplir]` |
| **Couverture de Test** | `> 85%` | `[À remplir]` | `[À remplir]` |

### 6.2. Bilan du Refactoring
*(Section à remplir une fois le travail terminé)*
- **Leçons apprises :**
    - `[Ex: La validation manuelle à chaque itération du Cycle de Migration a été cruciale pour garantir l'absence de régressions fonctionnelles.]`
- **Ce qui a bien fonctionné :** `[ex: La validation du harnais en Étape 1 a immédiatement détecté que nos tests initiaux ne vérifiaient pas l'écriture sur le disque. La migration incrémentale a permis de gérer les échecs de manière contrôlée.]`
- **Ce qui a été difficile :** `[ex: Le câblage DI a été plus complexe que prévu. La validation manuelle a requis une attention particulière pour chaque unité.]`
- **Leçons apprises :** `[ex: L'approche itérative a permis de limiter les risques en validant chaque unité individuellement.]`
- **Prochaines étapes / Améliorations futures :** `[ex: Maintenant que l'architecture est propre, nous pouvons facilement implémenter une cible de log pour un service externe comme Seq ou ELK.]`