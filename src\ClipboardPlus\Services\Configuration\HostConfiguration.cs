using Microsoft.Extensions.DependencyInjection;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Core.Services.SupprimerTout;
using ClipboardPlus.Core.Services.Visibility;
using ClipboardPlus.Core.Services.Deletion;
using ClipboardPlus.Modules.Core.Events;
using ClipboardPlus.Core.DataModels;
using System.Collections.Generic;
using ClipboardPlus.UI.ViewModels;
using ClipboardPlus.UI.ViewModels.Construction;
using ClipboardPlus.UI.ViewModels.Construction.DependencyInjection;
using ClipboardPlus.Diagnostics;
using ClipboardPlus.UI.Controls;
using System;
using System.Linq;
using ClipboardPlus.Utils;
using ClipboardPlus.Services;
using System.Windows.Threading;
using ClipboardPlus.UI.Models;
using ClipboardPlus.Core.Services.Logging;
using ClipboardPlus.Services.ContentHandlers;
using System.IO;
using System.Collections.Generic;
using ClipboardPlus.Core.Services.SystemTray;
using ClipboardPlus.Core.Services.Windows;
using Prism.Events;
using ClipboardPlus.Core.Services.UI;
using Prism.Events;

namespace ClipboardPlus.Services.Configuration
{
    public static class HostConfiguration
    {
        public static IServiceProvider ConfigureServices()
        {
            var services = new ServiceCollection();

            // ✅ CONFIGURATION LOGGING AVEC INJECTION DE DÉPENDANCES COMPLÈTE
            ConfigureLoggingServices(services);

            // SUPPRIMÉ 2025-01-13 : Cast invalide vers IAdvancedLoggingService
            // LoggingService n'implémente pas IAdvancedLoggingService
            // services.AddSingleton<IAdvancedLoggingService>(provider =>
            //     (IAdvancedLoggingService)provider.GetRequiredService<ILoggingService>());
            services.AddSingleton<IClipboardHistoryManager>(provider =>
            {
                var logger = provider.GetRequiredService<ILoggingService>();
                logger.LogInfo("🔧 [DIAGNOSTIC] CRÉATION ClipboardHistoryManager - Singleton avec services de suppression refactorisés");

                var manager = new ClipboardHistoryManager(
                    provider.GetRequiredService<IPersistenceService>(),
                    provider.GetRequiredService<ISettingsManager>(),
                    provider.GetRequiredService<IClipboardInteractionService>(),
                    provider.GetRequiredService<IDeletionValidator>(),
                    provider.GetRequiredService<IDeletionMemoryService>(),
                    provider.GetRequiredService<IDeletionRetryService>(),
                    provider.GetRequiredService<IDeletionStateManager>(),
                    provider.GetRequiredService<IDeletionNotificationService>()
                );

                logger.LogInfo($"✅ [DIAGNOSTIC] ClipboardHistoryManager créé - HashCode: {manager.GetHashCode()}");

                // Validation immédiate du Manager créé
                try
                {
                    var testItems = manager.HistoryItems;
                    logger.LogInfo($"✅ [DIAGNOSTIC] Manager validé - HistoryItems accessible: {testItems != null}");
                }
                catch (Exception ex)
                {
                    logger.LogError($"❌ [DIAGNOSTIC] Manager défaillant à la création: {ex.Message}", ex);
                }

                return manager;
            });

            // Service de santé des collections
            services.AddSingleton<ICollectionHealthService, CollectionHealthService>();

            // Services SystemTray
            services.AddSingleton<IThreadValidator, ThreadValidator>();
            services.AddSingleton<INotifyIconCleanupService, NotifyIconCleanupService>();
            services.AddSingleton<INotifyIconFactory, NotifyIconFactory>();
            services.AddSingleton<IIconResourceLoader, IconResourceLoader>();
            services.AddSingleton<IContextMenuBuilder, ContextMenuBuilder>();
            services.AddSingleton<IVisibilityManager, VisibilityManager>();
            services.AddSingleton<IStartupNotificationService, StartupNotificationService>();
            services.AddSingleton<ISystemTrayOrchestrator, SystemTrayOrchestrator>();

            // Services de fenêtres pour éliminer l'anti-pattern Service Locator
            services.AddSingleton<IHistoryWindowService, HistoryWindowService>();
            services.AddSingleton<ISettingsWindowService, SettingsWindowService>();

            services.AddSingleton<ISystemTrayService, SystemTrayService>();
            services.AddSingleton<IPersistenceService, PersistenceService>();
            services.AddSingleton<ISettingsManager>(provider =>
                new SettingsManager(
                    provider.GetRequiredService<IPersistenceService>(),
                    provider.GetRequiredService<ILoggingService>()
                ));
            services.AddSingleton<IUserThemeManager>(provider =>
                new UserThemeManager(provider.GetService<ILoggingService>()));
            services.AddSingleton<IWindowsHotkeyApi>(provider =>
                new WindowsHotkeyApi(provider.GetService<ILoggingService>()));
            services.AddSingleton<IGlobalShortcutService, GlobalShortcutService>();

            // Nouveaux services pour l'écoute du presse-papier
            services.AddSingleton<IDispatcherService, WpfDispatcherService>();
            services.AddSingleton<ClipboardListenerOptions>();
            services.AddSingleton<IClipboardListenerService, ClipboardListenerService>();

            // Ancien service ClipboardMonitorService supprimé - remplacé par ClipboardListenerService

            services.AddSingleton<ISystemStartupManager, SystemStartupManager>();
            services.AddSingleton<IDeletionDiagnostic, ClipboardPlus.UI.Controls.DeletionDiagnostic>();
            services.AddSingleton<IClipboardInteractionService, ClipboardInteractionService>();
            services.AddSingleton<IClipboardProcessorService, ClipboardProcessorService>();
            services.AddSingleton<IApplicationLifetimeManager>(provider =>
                new ApplicationLifetimeManager(provider));
            services.AddSingleton<IApplicationExitService, ApplicationExitService>();
            services.AddSingleton<ISingleInstanceService, SingleInstanceService>();
            services.AddSingleton<IUserNotificationService, WpfUserNotificationService>();

            // ✅ NOUVEAUX SERVICES UI POUR ARCHITECTURE SOLID - OpenSettings Refactoring
            services.AddSingleton<IWindowStateManager, Services.UI.WindowStateManager>();
            services.AddSingleton<IServiceContainerValidator, Services.UI.ServiceContainerValidator>();
            services.AddSingleton<IModalWindowOrchestrator, Services.UI.ModalWindowOrchestrator>();

            // ✅ PRISM EVENTAGGREGATOR - Communication inter-modules robuste
            services.AddSingleton<IEventAggregator, EventAggregator>();

            // ✅ MODULES ARCHITECTURE - Enregistrement des modules avec Prism EventAggregator
            services.AddSingleton<ClipboardPlus.Modules.History.IHistoryModule, ClipboardPlus.Modules.History.HistoryModule>();
            services.AddSingleton<ClipboardPlus.Modules.Commands.ICommandModule, ClipboardPlus.Modules.Commands.CommandModule>();
            services.AddSingleton<ClipboardPlus.Modules.Creation.ICreationModule, ClipboardPlus.Modules.Creation.CreationModule>();

            // Ajouter le gestionnaire global d'exceptions
            services.AddSingleton<IGlobalExceptionManager>(provider =>
                new GlobalExceptionManager(
                    provider,
                    Dispatcher.CurrentDispatcher
                ));

            // Service IKeyboardShortcutService supprimé - non utilisé (remplacé par IGlobalShortcutService)

            // Ajouter le service de gestion des notifications
            services.AddSingleton<INotificationService, WindowsNotificationService>();

            // Service avancé de gestion du presse-papiers supprimé - remplacé par ClipboardListenerService + ClipboardInteractionService

            // Ajouter les nouveaux services de refactorisation
            services.AddSingleton<IStartupOrchestrator, StartupOrchestrator>();
            services.AddSingleton<ICommandLineParser, CommandLineParser>();

            // Services de refactorisation ClipboardHistoryManager_HistoryChanged
            services.AddSingleton<IHistoryChangeValidator, HistoryChangeValidator>();
            services.AddSingleton<IHistoryChangeThreadingService, HistoryChangeThreadingService>();
            services.AddSingleton<IHistorySynchronizationService, HistorySynchronizationService>();
            services.AddSingleton<IHistoryMaintenanceService, HistoryMaintenanceService>();
            services.AddSingleton<IHistoryChangeOrchestrator, HistoryChangeOrchestrator>();

            // Services de refactorisation DeleteItemAsync
            services.AddSingleton<IDeletionValidator, DeletionValidator>();
            services.AddSingleton<IDeletionMemoryService, DeletionMemoryService>();
            services.AddSingleton<IDeletionRetryService, DeletionRetryService>();
            services.AddSingleton<IDeletionStateManager, DeletionStateManager>();

            // ✅ CORRECTION CRITIQUE: DeletionNotificationService avec callback HistoryChanged
            services.AddSingleton<IDeletionNotificationService>(provider =>
            {
                var loggingService = provider.GetRequiredService<ILoggingService>();

                loggingService.LogInfo("[HostConfiguration] Création du DeletionNotificationService avec callback personnalisé");

                // Créer un callback qui déclenche l'événement HistoryChanged du ClipboardHistoryManager
                Action historyChangedCallback = () =>
                {
                    try
                    {
                        loggingService.LogInfo("[DeletionNotificationService] Callback HistoryChanged appelé");

                        // Récupérer le ClipboardHistoryManager et déclencher son événement HistoryChanged
                        var historyManager = provider.GetRequiredService<IClipboardHistoryManager>();
                        historyManager.NotifyHistoryChanged();

                        loggingService.LogInfo("[DeletionNotificationService] Événement HistoryChanged du ClipboardHistoryManager déclenché via NotifyHistoryChanged()");
                    }
                    catch (Exception ex)
                    {
                        loggingService.LogError($"[DeletionNotificationService] Erreur lors du déclenchement HistoryChanged: {ex.Message}", ex);
                    }
                };

                var service = new DeletionNotificationService(loggingService, historyChangedCallback);
                loggingService.LogInfo("[HostConfiguration] DeletionNotificationService créé avec succès");
                return service;
            });

            // === SERVICES POUR SUPPRESSION D'ÉLÉMENTS ===
            services.AddSingleton<IDeletionUIValidator, DeletionUIValidator>();
            services.AddSingleton<IDeletionUIHandler, DeletionUIHandler>();
            services.AddSingleton<IDeletionUINotificationService, DeletionUINotificationService>();
            services.AddSingleton<IDeletionService, DeletionService>();

            // Service de feature flags pour la migration progressive
            services.AddSingleton<IFeatureFlagService, FeatureFlagService>();

            // Enregistrement du service d'interaction utilisateur
            services.AddSingleton<ClipboardPlus.Core.Services.IUserInteractionService, ClipboardPlus.UI.Services.UserInteractionService>();

            // Service de renommage (refactorisation ConfirmerRenommage)
            services.AddSingleton<IRenameService, RenameService>();

            // === SERVICES DE VISIBILITÉ ===
            // Règles de visibilité spécialisées (Strategy Pattern)
            services.AddSingleton<IVisibilityRule<ClipboardItem>, TitleVisibilityRule>(provider =>
                new TitleVisibilityRule(provider.GetRequiredService<ILoggingService>()));
            services.AddSingleton<IVisibilityRule<ClipboardItem>, TimestampVisibilityRule>(provider =>
                new TimestampVisibilityRule(provider.GetRequiredService<ILoggingService>()));

            // Gestionnaire d'état centralisé (SRP + DIP)
            services.AddSingleton<IVisibilityStateManager>(provider =>
                new VisibilityStateManager(
                    provider.GetServices<IVisibilityRule<ClipboardItem>>().First(r => r is TitleVisibilityRule),
                    provider.GetServices<IVisibilityRule<ClipboardItem>>().First(r => r is TimestampVisibilityRule),
                    provider.GetRequiredService<ISettingsManager>(),
                    provider.GetRequiredService<ILoggingService>()
                ));

            // Ajouter le modèle AppSettings qui encapsule les paramètres globaux
            // AppSettings utilise maintenant IVisibilityStateManager
            services.AddSingleton<AppSettings>(provider =>
                new AppSettings(
                    provider.GetRequiredService<ISettingsManager>(),
                    provider.GetRequiredService<IVisibilityStateManager>(),
                    provider.GetRequiredService<ILoggingService>()
                ));

            // Services de refactorisation LogDeletionResult (Phase 2)
            services.AddSingleton<ClipboardPlus.Core.Services.LogDeletionResult.Interfaces.ICollectionStateAnalyzer,
                ClipboardPlus.Core.Services.LogDeletionResult.Implementations.CollectionStateAnalyzer>();
            services.AddSingleton<ClipboardPlus.Core.Services.LogDeletionResult.Interfaces.IDeletionResultValidator,
                ClipboardPlus.Core.Services.LogDeletionResult.Implementations.DeletionResultValidator>();
            services.AddSingleton<ClipboardPlus.Core.Services.LogDeletionResult.Interfaces.IDeletionResultFormatter,
                ClipboardPlus.Core.Services.LogDeletionResult.Implementations.DeletionResultFormatter>();
            services.AddSingleton<ClipboardPlus.Core.Services.LogDeletionResult.Interfaces.IDeletionMetricsCollector,
                ClipboardPlus.Core.Services.LogDeletionResult.Implementations.DeletionMetricsCollector>();
            services.AddSingleton<ClipboardPlus.Core.Services.LogDeletionResult.Interfaces.IDeletionResultLogger,
                ClipboardPlus.Core.Services.LogDeletionResult.Implementations.DeletionResultLogger>();

            // Services de migration LogDeletionResult supprimés - Migration terminée

            // === SERVICES CLIPBOARDHISTORYVIEWMODEL ===
            // Configuration des services pour ClipboardHistoryViewModel
            services.AddClipboardHistoryViewModelSOLIDArchitecture();

            // ✅ ARCHITECTURE MANAGÉRIALE - Enregistrement des 6 managers spécialisés
            services.AddSingleton<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IHistoryViewModelManager>(provider =>
                new ClipboardPlus.UI.ViewModels.Managers.Implementations.HistoryViewModelManager(
                    provider.GetRequiredService<ClipboardPlus.Modules.History.IHistoryModule>(),
                    provider.GetRequiredService<ILoggingService>() // ✅ CORRECTION CRITIQUE !
                ));
            services.AddSingleton<ClipboardPlus.UI.ViewModels.Managers.Interfaces.ICommandViewModelManager>(provider =>
                new ClipboardPlus.UI.ViewModels.Managers.Implementations.CommandViewModelManager(
                    provider.GetRequiredService<ClipboardPlus.Modules.Commands.ICommandModule>()
                ));
            services.AddSingleton<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IItemCreationManager>(provider =>
                new ClipboardPlus.UI.ViewModels.Managers.Implementations.ItemCreationManager(
                    provider.GetRequiredService<ClipboardPlus.Modules.Creation.ICreationModule>()
                ));
            services.AddSingleton<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IEventViewModelManager, ClipboardPlus.UI.ViewModels.Managers.Implementations.EventViewModelManager>();
            services.AddSingleton<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IVisibilityViewModelManager, ClipboardPlus.UI.ViewModels.Managers.Implementations.VisibilityViewModelManager>();
            services.AddSingleton<ClipboardPlus.UI.ViewModels.Managers.Interfaces.IDragDropViewModelManager, ClipboardPlus.UI.ViewModels.Managers.Implementations.DragDropViewModelManager>();

            // Enregistrement des ViewModels avec ARCHITECTURE MANAGÉRIALE PURE
            services.AddTransient<ClipboardHistoryViewModel>(provider =>
            {
                // 🚨 CORRECTION FINALE : Utiliser CreateWithDependencyInjection qui fait la vérification des managers
                return ClipboardHistoryViewModelFactory.CreateWithDependencyInjection(
                    provider,
                    provider.GetRequiredService<IClipboardHistoryManager>(),
                    provider.GetRequiredService<IClipboardInteractionService>(),
                    provider.GetRequiredService<ISettingsManager>(),
                    provider.GetRequiredService<IUserNotificationService>(),
                    provider.GetRequiredService<ClipboardPlus.Core.Services.IUserInteractionService>(),
                    provider.GetRequiredService<IRenameService>(),
                    provider.GetRequiredService<ClipboardPlus.Core.Services.LogDeletionResult.Interfaces.IDeletionResultLogger>(),
                    provider.GetService<ICollectionHealthService>(),
                    provider.GetService<IVisibilityStateManager>()
                );
            });
            services.AddTransient<ContentPreviewViewModel>();
            services.AddTransient<AppSettingsViewModel>(provider =>
                new AppSettingsViewModel(
                    provider.GetRequiredService<ISettingsManager>(),
                    provider.GetRequiredService<IUserThemeManager>(),
                    provider.GetRequiredService<IGlobalShortcutService>(),
                    provider.GetRequiredService<IUserNotificationService>(),
                    provider.GetRequiredService<ILoggingService>()
                ));
            // services.AddTransient<SettingsViewModel>();

            // Service de gestion des événements du presse-papiers
            services.AddSingleton<IClipboardEventHandler, ClipboardEventHandlerService>();

            services.AddSingleton<IStartupLogic, StartupLogic>();

            // Services SupprimerTout - Architecture modulaire
            services.AddSingleton<SupprimerToutValidator>();
            services.AddSingleton<SupprimerToutAnalyzer>();
            services.AddSingleton<SupprimerToutUIHandler>();
            services.AddSingleton<SupprimerToutExecutor>();
            services.AddSingleton<SupprimerToutOrchestrator>();

            // ===== SERVICES CONTENT PREVIEW =====
            // Services utilitaires
            services.AddSingleton<IFileSystemService, FileSystemService>();
            services.AddSingleton<IBitmapImageFactory, BitmapImageFactory>();
            services.AddSingleton<IFileSizeFormatter, FileSizeFormatter>();

            // Content Handlers - Pattern Strategy
            services.AddTransient<IContentHandler, TextContentHandler>();
            services.AddTransient<IContentHandler, HtmlContentHandler>();
            services.AddTransient<IContentHandler, RtfContentHandler>();
            services.AddTransient<IContentHandler, ImageContentHandler>();
            services.AddTransient<IContentHandler, FilePathContentHandler>();

            // Factory Pattern pour les handlers
            services.AddSingleton<IContentHandlerFactory, ContentHandlerFactory>();

            // Service principal orchestrateur
            services.AddSingleton<IContentPreviewLoader, ContentPreviewLoader>();

            // ===== SERVICES ORCHESTRATEURS CLIPBOARD =====
            services.AddSingleton<ClipboardPlus.Core.Services.Interfaces.IClipboardItemOrchestrator, ClipboardPlus.Core.Services.Implementations.ClipboardItemOrchestrator>();

            // Services SOLID pour l'orchestrateur
            services.AddSingleton<ClipboardPlus.Core.Services.Interfaces.IClipboardItemValidator, ClipboardPlus.Core.Services.Implementations.ClipboardItemValidator>();
            services.AddSingleton<ClipboardPlus.Core.Services.Interfaces.IDuplicateDetector, ClipboardPlus.Core.Services.Implementations.DuplicateDetector>();
            services.AddSingleton<ClipboardPlus.Core.Services.Interfaces.IClipboardItemProcessor, ClipboardPlus.Core.Services.Implementations.ClipboardItemProcessor>();
            services.AddSingleton<ClipboardPlus.Core.Services.Interfaces.IHistoryManager, ClipboardPlus.Core.Services.Implementations.HistoryManager>();
            services.AddSingleton<ClipboardPlus.Core.Services.Interfaces.IEventNotifier, ClipboardPlus.Core.Services.Implementations.EventNotifier>();
            services.AddSingleton<ClipboardPlus.Core.Services.Interfaces.IOperationLogger, ClipboardPlus.Core.Services.Implementations.OperationLogger>();

            // ===== SERVICES NEWITEM =====
            services.AddSingleton<ClipboardPlus.Core.Services.NewItem.Interfaces.INewItemValidationService, ClipboardPlus.Core.Services.NewItem.Implementations.NewItemValidationService>();
            services.AddSingleton<ClipboardPlus.Core.Services.NewItem.Interfaces.IDialogConfigurationService, ClipboardPlus.Core.Services.NewItem.Implementations.DialogConfigurationService>();
            services.AddSingleton<ClipboardPlus.Core.Services.NewItem.Interfaces.ITestModeHandler, ClipboardPlus.Core.Services.NewItem.Implementations.TestModeHandler>();
            services.AddSingleton<ClipboardPlus.Core.Services.NewItem.Interfaces.ITestEnvironmentDetector, ClipboardPlus.Core.Services.NewItem.Implementations.TestEnvironmentDetector>();
            services.AddSingleton<ClipboardPlus.Core.Services.NewItem.Interfaces.IDialogService, ClipboardPlus.Core.Services.NewItem.Implementations.DialogService>();
            services.AddSingleton<ClipboardPlus.Core.Services.NewItem.Interfaces.IErrorHandlingService, ClipboardPlus.Core.Services.NewItem.Implementations.ErrorHandlingService>();
            // Note: IOperationStateManager et INewItemCreationOrchestrator sont créés dynamiquement dans le ViewModel

            var serviceProvider = services.BuildServiceProvider();
            // Initialiser le service de journalisation immédiatement
            try
            {
                var loggingService = serviceProvider.GetService<ILoggingService>();
                if (loggingService != null)
                {
                    loggingService.LogInfo("Services configurés et initialisés avec succès");
                }
            }
            catch (Exception ex)
            {
                // Si même la journalisation échoue, utiliser Debug.WriteLine
                System.Diagnostics.Debug.WriteLine($"Erreur d'initialisation du service de journalisation: {ex.Message}");
            }
            return serviceProvider;
        }

        /// <summary>
        /// Configure les services de logging avec injection de dépendances complète.
        /// Respecte parfaitement le principe d'Inversion de Dépendances (DIP).
        /// </summary>
        private static void ConfigureLoggingServices(IServiceCollection services)
        {
            // 🆕 Configuration du logging
            var logFilePath = GetLogFilePath();
            var loggingConfig = new LoggingConfiguration
            {
                LogFilePath = logFilePath,
                ConsoleOutputEnabled = true,
                DebugOutputEnabled = true,
                MinimumLevel = "DEBUG",
                MaxBufferSize = 100
            };

            // Debug : Afficher le chemin de log
            System.Diagnostics.Debug.WriteLine($"[DI CONFIG] Chemin de log configuré : {logFilePath}");
            Console.WriteLine($"[DI CONFIG] Chemin de log configuré : {logFilePath}");

            // 🆕 Enregistrement de la configuration
            services.AddSingleton<ILoggingConfiguration>(loggingConfig);

            // 🆕 Enregistrement de la factory
            services.AddSingleton<ILogEntryFactory, LogEntryFactory>();

            // 🆕 Enregistrement des targets individuels
            services.AddSingleton<ILogTarget, DebugLogTarget>();
            services.AddSingleton<ILogTarget>(provider =>
            {
                var config = provider.GetRequiredService<ILoggingConfiguration>();
                return new ConsoleLogTarget(config.ConsoleOutputEnabled);
            });
            services.AddSingleton<ILogTarget>(provider =>
            {
                var config = provider.GetRequiredService<ILoggingConfiguration>();
                return new FileLogTarget(config.LogFilePath);
            });

            // 🎯 Service principal avec injection complète
            services.AddSingleton<ILoggingService>(provider =>
            {
                var factory = provider.GetRequiredService<ILogEntryFactory>();
                var targets = provider.GetServices<ILogTarget>();
                var config = provider.GetRequiredService<ILoggingConfiguration>();

                return new LoggingService(factory, targets, config);
            });
        }

        /// <summary>
        /// Génère le chemin du fichier de log dans le dossier logs à la racine du projet.
        /// </summary>
        private static string GetLogFilePath()
        {
            try
            {
                // Remonter depuis le répertoire bin vers la racine du projet
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                var projectRoot = FindProjectRoot(baseDirectory);
                var logsDirectory = Path.Combine(projectRoot, "logs");

                // S'assurer que le répertoire logs existe
                if (!Directory.Exists(logsDirectory))
                {
                    Directory.CreateDirectory(logsDirectory);
                }

                var fileName = $"clipboard_plus_{DateTime.Now:yyyyMMdd}.log";
                return Path.Combine(logsDirectory, fileName);
            }
            catch (Exception ex)
            {
                // En cas d'erreur, utiliser un fallback dans le répertoire de base
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                var fallbackPath = Path.Combine(baseDirectory, "clipboard_plus_fallback.log");

                System.Diagnostics.Debug.WriteLine($"Erreur création chemin log: {ex.Message}, utilisation fallback: {fallbackPath}");
                Console.WriteLine($"Erreur création chemin log: {ex.Message}, utilisation fallback: {fallbackPath}");

                return fallbackPath;
            }
        }

        /// <summary>
        /// Trouve le répertoire racine du projet en remontant depuis le répertoire de l'exécutable.
        /// </summary>
        private static string FindProjectRoot(string startDirectory)
        {
            var current = new DirectoryInfo(startDirectory);

            // Remonter jusqu'à trouver le répertoire qui contient src/ ou .git/ ou .sln
            while (current != null)
            {
                if (Directory.Exists(Path.Combine(current.FullName, "src")) ||
                    Directory.Exists(Path.Combine(current.FullName, ".git")) ||
                    Directory.GetFiles(current.FullName, "*.sln").Length > 0)
                {
                    return current.FullName;
                }
                current = current.Parent;
            }

            // Si on ne trouve pas, utiliser le répertoire de départ
            return startDirectory;
        }
    }
}